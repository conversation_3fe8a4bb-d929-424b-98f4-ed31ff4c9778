#include <stdint.h>
#include <stddef.h>
#include <string.h>
#include <stdio.h>

/**
 * 可配置的LibAFL兼容fuzzing目标
 * 
 * 使用编译标志控制调试输出：
 * - 默认：静默模式（生产环境）
 * - -DFUZZ_DEBUG：启用调试输出
 * 
 * 编译示例：
 * clang -shared -fPIC simple_fuzzer_target_configurable.c -o target.so          # 静默模式
 * clang -shared -fPIC -DFUZZ_DEBUG simple_fuzzer_target_configurable.c -o target.so  # 调试模式
 */

// 调试宏定义
#ifdef FUZZ_DEBUG
    #define DEBUG_PRINT(...) printf(__VA_ARGS__)
#else
    #define DEBUG_PRINT(...) do {} while(0)
#endif

// LibAFL需要的入口函数 - 必须使用这个确切的签名
int LLVMFuzzerTestOneInput(const uint8_t *data, size_t size) {
    // 边界检查
    if (size < 4) {
        return 0;
    }
    
    // 第一个分支：检查魔术字符串 "FUZZ"
    if (data[0] == 'F' && data[1] == 'U' && data[2] == 'Z' && data[3] == 'Z') {
        DEBUG_PRINT("Magic found: FUZZ\n");
        
        // 嵌套分支1：检查扩展魔术字符串
        if (size > 10 && data[4] == 'L' && data[5] == 'M') {
            DEBUG_PRINT("Extended magic: FUZZLM\n");
            
            // 更深的嵌套：检查完整字符串
            if (size > 15 && memcmp(data + 6, "-AGENT", 6) == 0) {
                DEBUG_PRINT("Full magic: FUZZLM-AGENT\n");
                
                // 潜在的有趣路径
                if (size > 20 && data[12] == 'X') {
                    DEBUG_PRINT("Special marker found!\n");
                    
                    // 添加一个可能触发bug的条件
                    if (data[13] == 'Y' && data[14] == 'Z') {
                        // 模拟一个潜在的bug（但不会真的崩溃）
                        volatile int x = 0;
                        if (data[15] == '!') {
                            x = 1;
                            DEBUG_PRINT("Interesting condition triggered: %d\n", x);
                        }
                    }
                }
            }
        }
        
        // 嵌套分支2：数值范围检查
        if (size > 8) {
            uint32_t value = *(uint32_t*)(data + 4);
            if (value > 0x1000 && value < 0x2000) {
                DEBUG_PRINT("Value in range: 0x%x\n", value);
            } else if (value >= 0x2000 && value < 0x3000) {
                DEBUG_PRINT("Value in higher range: 0x%x\n", value);
            }
        }
    }
    
    // 第二个主分支：检查另一个模式
    if (size >= 8 && data[0] == 'T' && data[1] == 'E' && data[2] == 'S' && data[3] == 'T') {
        DEBUG_PRINT("Test pattern found\n");
        
        // 检查后续字节
        for (size_t i = 4; i < size && i < 8; i++) {
            if (data[i] >= 'A' && data[i] <= 'Z') {
                DEBUG_PRINT("Capital letter at position %zu: %c\n", i, data[i]);
            }
        }
    }
    
    // 第三个分支：二进制数据处理
    if (size >= 16) {
        uint64_t checksum = 0;
        for (size_t i = 0; i < 16; i++) {
            checksum ^= data[i];
        }
        
        if (checksum == 0xFF) {
            DEBUG_PRINT("Checksum match!\n");
        }
    }
    
    // 正常返回
    return 0;
}

// 可选的初始化函数（LibAFL会在开始时调用一次）
int LLVMFuzzerInitialize(int *argc, char ***argv) {
    DEBUG_PRINT("Fuzzer initialized with LibAFL\n");
    return 0;
}