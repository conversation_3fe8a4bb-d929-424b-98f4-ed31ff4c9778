#include <stdint.h>
#include <stddef.h>
#include <string.h>
#include <stdio.h>

/**
 * 静默版本的LibAFL兼容fuzzing目标
 * 
 * 移除了所有printf语句，避免日志污染
 * 保留所有分支逻辑用于coverage测试
 */

// LibAFL需要的入口函数 - 必须使用这个确切的签名
int LLVMFuzzerTestOneInput(const uint8_t *data, size_t size) {
    // 边界检查
    if (size < 4) {
        return 0;
    }
    
    // 第一个分支：检查魔术字符串 "FUZZ"
    if (data[0] == 'F' && data[1] == 'U' && data[2] == 'Z' && data[3] == 'Z') {
        // 静默执行，不输出
        
        // 嵌套分支1：检查扩展魔术字符串
        if (size > 10 && data[4] == 'L' && data[5] == 'M') {
            // 静默执行
            
            // 更深的嵌套：检查完整字符串
            if (size > 15 && memcmp(data + 6, "-AGENT", 6) == 0) {
                // 静默执行
                
                // 潜在的有趣路径
                if (size > 20 && data[12] == 'X') {
                    // 静默执行
                    
                    // 添加一个可能触发bug的条件
                    if (data[13] == 'Y' && data[14] == 'Z') {
                        // 模拟一个潜在的bug（但不会真的崩溃）
                        volatile int x = 0;
                        if (data[15] == '!') {
                            x = 1;
                            // 移除printf，仅保留逻辑
                        }
                    }
                }
            }
        }
        
        // 嵌套分支2：数值范围检查
        if (size > 8) {
            uint32_t value = *(uint32_t*)(data + 4);
            if (value > 0x1000 && value < 0x2000) {
                // 静默执行
            } else if (value >= 0x2000 && value < 0x3000) {
                // 静默执行
            }
        }
    }
    
    // 第二个主分支：检查另一个模式
    if (size >= 8 && data[0] == 'T' && data[1] == 'E' && data[2] == 'S' && data[3] == 'T') {
        // 静默执行
        
        // 检查后续字节
        for (size_t i = 4; i < size && i < 8; i++) {
            if (data[i] >= 'A' && data[i] <= 'Z') {
                // 静默执行
            }
        }
    }
    
    // 第三个分支：二进制数据处理
    if (size >= 16) {
        uint64_t checksum = 0;
        for (size_t i = 0; i < 16; i++) {
            checksum ^= data[i];
        }
        
        if (checksum == 0xFF) {
            // 静默执行
        }
    }
    
    // 正常返回
    return 0;
}

// 可选的初始化函数（LibAFL会在开始时调用一次）
int LLVMFuzzerInitialize(int *argc, char ***argv) {
    // 静默初始化
    return 0;
}