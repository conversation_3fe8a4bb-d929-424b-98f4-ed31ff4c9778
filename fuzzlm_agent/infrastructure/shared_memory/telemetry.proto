// FuzzLM-Agent 遥测数据protobuf定义
// 用于Python和Rust之间的跨语言数据同步
syntax = "proto3";

package fuzzlm.telemetry;

// 遥测数据类型枚举
enum TelemetryDataType {
  EXECUTION_COUNT = 0;
  COVERAGE_HIT = 1;
  CRASH_FOUND = 2;
  QUEUE_UPDATE = 3;
  ENERGY_UPDATE = 4;
  CORPUS_GROW = 5;
  HANG_FOUND = 6;
  MUTATOR_STATS = 7;
  SCHEDULER_STATS = 8;
  FEEDBACK_SCORE = 9;
}

// 遥测条目 - 顶层消息
message TelemetryEntry {
  TelemetryDataType data_type = 1;
  string instance_id = 2;
  uint64 timestamp_ns = 3;
  
  // 使用oneof确保每个条目只包含一种数据类型
  oneof payload {
    ExecutionStats execution_stats = 10;
    CoverageHit coverage_hit = 11;
    CrashFound crash_found = 12;
    QueueUpdate queue_update = 13;
    EnergyUpdate energy_update = 14;
    CorpusGrow corpus_grow = 15;
    HangFound hang_found = 16;
    MutatorStats mutator_stats = 17;
    SchedulerStats scheduler_stats = 18;
    FeedbackScore feedback_score = 19;
  }
}

// 执行统计数据
message ExecutionStats {
  uint32 executions = 1;
  float exec_per_sec = 2;
  uint32 corpus_size = 3;
  uint32 crashes = 4;
  uint32 coverage_edges = 5;  // 添加覆盖率边数字段
}

// 覆盖率命中数据
message CoverageHit {
  uint32 edge_id = 1;
  uint32 hit_count = 2;
  bool is_new = 3;
}

// 崩溃发现数据
message CrashFound {
  string crash_type = 1;
  uint64 input_hash = 2;
  uint32 signal = 3;
}

// 队列更新数据
message QueueUpdate {
  uint32 queue_size = 1;
  uint32 pending_favored = 2;
  uint32 cycles_done = 3;
}

// 能量更新数据
message EnergyUpdate {
  uint32 energy = 1;
  float energy_ratio = 2;
  uint32 executions_left = 3;
}

// 语料库增长数据
message CorpusGrow {
  uint32 new_inputs = 1;
  uint32 total_size = 2;
  uint32 avg_length = 3;
}

// 挂起发现数据
message HangFound {
  uint64 input_hash = 1;
  uint32 timeout_ms = 2;
}

// 变异器统计数据
message MutatorStats {
  uint32 mutator_id = 1;
  uint32 usage_count = 2;
  float success_rate = 3;
}

// 调度器统计数据  
message SchedulerStats {
  uint32 scheduler_id = 1;
  uint32 current_favored = 2;
  uint32 total_cal_cycles = 3;
}

// 反馈评分数据
message FeedbackScore {
  uint32 feedback_id = 1;
  float score = 2;
  uint32 hit_count = 3;
}

// 批量遥测消息 - 用于高性能批量传输
message TelemetryBatch {
  repeated TelemetryEntry entries = 1;
  uint64 batch_timestamp_ns = 2;
  uint32 batch_id = 3;
}