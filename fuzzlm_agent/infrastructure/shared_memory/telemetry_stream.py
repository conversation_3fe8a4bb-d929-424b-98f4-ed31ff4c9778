"""Telemetry Stream - 基于protobuf的共享内存遥测数据读取器
========================================================

基于共享内存的高性能遥测数据流读取器, 用于从Rust fuzzing engine
读取实时遥测数据。现在使用protobuf格式以实现更好的跨语言兼容性。
"""

from __future__ import annotations

import asyncio
import logging
import mmap
import struct
import time
from collections import deque
from dataclasses import dataclass, field
from datetime import datetime, timezone
from pathlib import Path
from typing import Any

from .telemetry_pb2 import (  # type: ignore[attr-defined]
    TelemetryDataType,
    TelemetryEntry,
)

logger = logging.getLogger(__name__)

# 显式导出protobuf类型以满足mypy检查
__all__ = [
    "TelemetryDataPlane",
    "TelemetryDataType",
    "TelemetryEntry",
    "TelemetryReader",
    "TelemetryHealthMonitor",
    "create_telemetry_reader",
]


@dataclass
class TelemetryEntryDict:
    """遥测数据条目字典包装器"""

    type: str
    instance_id: str
    timestamp: float
    data: dict[str, Any]

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "type": self.type,
            "instance_id": self.instance_id,
            "timestamp": self.timestamp,
            **self.data,
        }


@dataclass
class DataValidationStats:
    """数据验证统计信息"""

    # 序列号跟踪
    last_sequence_number: int = -1
    sequence_gaps: list[tuple[int, int]] = field(
        default_factory=list
    )  # (expected, actual)
    out_of_order_count: int = 0

    # 数据完整性
    corrupted_entries: int = 0
    invalid_protobuf: int = 0
    missing_fields: int = 0

    # 时间戳验证
    future_timestamps: int = 0
    stale_timestamps: int = 0
    timestamp_jumps: int = 0
    last_timestamp: float = 0.0

    # 数据率监控
    entries_read: int = 0
    bytes_read: int = 0
    last_update_time: float = field(default_factory=time.time)
    data_rate_bps: float = 0.0  # bytes per second
    entry_rate_eps: float = 0.0  # entries per second

    # 健康状态
    is_healthy: bool = True
    health_issues: list[str] = field(default_factory=list)

    def update_rates(self) -> None:
        """更新数据率统计"""
        current_time = time.time()
        time_delta = current_time - self.last_update_time

        if time_delta > 0:
            self.data_rate_bps = self.bytes_read / time_delta
            self.entry_rate_eps = self.entries_read / time_delta

            # 重置计数器
            self.bytes_read = 0
            self.entries_read = 0
            self.last_update_time = current_time


@dataclass
class TelemetryHealthStatus:
    """遥测系统健康状态"""

    stream_name: str
    connected: bool
    last_data_time: float | None = None
    data_staleness_seconds: float = 0.0
    buffer_utilization: float = 0.0
    validation_stats: DataValidationStats = field(default_factory=DataValidationStats)

    # 阈值配置
    stale_threshold_seconds: float = 60.0
    max_sequence_gap: int = 1000
    max_corruption_rate: float = 0.01  # 1%

    def check_health(self) -> tuple[bool, list[str]]:
        """检查健康状态并返回问题列表"""
        issues = []

        # 检查连接状态
        if not self.connected:
            issues.append("未连接到共享内存")
            return False, issues

        # 检查数据新鲜度
        if self.last_data_time:
            self.data_staleness_seconds = time.time() - self.last_data_time
            if self.data_staleness_seconds > self.stale_threshold_seconds:
                issues.append(f"数据过期 {self.data_staleness_seconds:.1f}秒")
        else:
            issues.append("从未接收到数据")

        # 检查序列号间隙
        if self.validation_stats.sequence_gaps:
            recent_gaps = self.validation_stats.sequence_gaps[-10:]
            max_gap = max(abs(expected - actual) for expected, actual in recent_gaps)
            if max_gap > self.max_sequence_gap:
                issues.append(f"检测到大序列号间隙: {max_gap}")

        # 检查数据损坏率
        total_entries = self.validation_stats.entries_read
        if total_entries > 0:
            corruption_rate = (
                self.validation_stats.corrupted_entries
                + self.validation_stats.invalid_protobuf
            ) / total_entries

            if corruption_rate > self.max_corruption_rate:
                issues.append(f"数据损坏率过高: {corruption_rate:.2%}")

        # 检查时间戳异常
        if self.validation_stats.future_timestamps > 0:
            issues.append(
                f"检测到{self.validation_stats.future_timestamps}个未来时间戳"
            )

        if self.validation_stats.timestamp_jumps > 10:
            issues.append(f"时间戳跳变过多: {self.validation_stats.timestamp_jumps}")

        # 检查缓冲区利用率
        if self.buffer_utilization > 0.9:
            issues.append(f"缓冲区利用率过高: {self.buffer_utilization:.1%}")

        is_healthy = len(issues) == 0
        return is_healthy, issues


class TelemetryDataPlane:
    """基于protobuf的共享内存遥测数据平面

    用于高性能地从Rust fuzzing engine读取遥测数据。
    使用内存映射文件实现低延迟的数据传输。
    """

    # 共享内存文件格式常量 - 与Rust实现保持一致
    MAGIC_NUMBER = 0x54454C45  # 'TELE' - 格式标识
    VERSION = 2  # 版本2表示protobuf格式
    METADATA_SIZE = 64  # 元数据区域大小(与Rust一致)
    ENTRY_HEADER_SIZE = 4  # 条目长度字段大小

    # 数据验证常量
    MAX_ENTRY_SIZE = 100000  # 100KB - 单个条目最大大小
    MIN_ENTRY_SIZE = 10  # 最小有效条目大小
    MAX_TIMESTAMP_DRIFT = 3600  # 1小时 - 最大时间戳偏差

    def __init__(self, stream_name: str = "champion_main"):
        """初始化遥测数据平面

        Args:
            stream_name: 共享内存流名称（不包含fuzzlm_telemetry_前缀）

        """
        self.stream_name = stream_name
        # Rust使用 fuzzlm_telemetry_{name} 格式
        self.file_name = f"fuzzlm_telemetry_{stream_name}"

        # 与Rust端保持一致的路径处理 - 修复路径同步问题
        import os

        base_path = os.environ.get("FUZZLM_SHM_DIR")
        if base_path is None:
            # 首先尝试找到实际的共享内存文件位置
            possible_paths = self._get_possible_telemetry_paths(self.file_name)

            # 如果找到了现有文件，使用其目录
            for path in possible_paths:
                if os.path.exists(path):
                    base_path = os.path.dirname(path)
                    logger.info(
                        f"Found existing telemetry file, using path: {base_path}"
                    )
                    break

            if base_path is None:
                # 使用项目根目录下的runtime/temp
                project_root = os.environ.get("FUZZLM_PROJECT_ROOT")
                if project_root:
                    base_path = os.path.join(project_root, "runtime", "temp")
                else:
                    # 尝试找到项目根目录，与Rust端逻辑保持一致
                    current_dir = os.getcwd()

                    # 优先检查fuzzing-engine目录（Rust进程的工作目录）
                    if "fuzzing-engine" in current_dir:
                        # 如果在fuzzing-engine目录，使用该目录下的runtime/temp
                        base_path = os.path.join(current_dir, "runtime", "temp")
                    elif "fuzzlm-agent" in current_dir:
                        # 找到包含fuzzlm-agent的部分
                        parts = current_dir.split(os.sep)
                        found_root = None
                        for i, part in enumerate(parts):
                            if part == "fuzzlm-agent":
                                found_root = os.sep.join(parts[: i + 1])
                                break
                        if found_root:
                            # 尝试fuzzing-engine路径优先
                            fuzzing_engine_path = os.path.join(
                                found_root,
                                "fuzzlm_agent",
                                "fuzzing-engine",
                                "runtime",
                                "temp",
                            )
                            if os.path.exists(fuzzing_engine_path):
                                base_path = fuzzing_engine_path
                            else:
                                base_path = os.path.join(found_root, "runtime", "temp")
                        else:
                            base_path = os.path.join(current_dir, "runtime", "temp")
                    else:
                        # 默认使用当前目录下的runtime/temp
                        base_path = os.path.join(current_dir, "runtime", "temp")

        self.base_path = base_path
        self.shm_path = Path(f"{base_path}/{self.file_name}")
        self.mmap_obj: mmap.mmap | None = None
        self.file_handle: Any | None = None
        self.read_position = 0
        self.connected = False

        # 数据验证统计
        self.validation_stats = DataValidationStats()
        self.health_status = TelemetryHealthStatus(
            stream_name=stream_name, connected=False
        )

        logger.info(
            f"Telemetry data plane initialized for stream: {stream_name} "
            f"(file: {self.file_name}) at {self.shm_path} (protobuf)"
        )

    def connect(self) -> bool:
        """连接到共享内存，支持路径查找和重试机制

        Returns:
            是否成功连接

        """
        if self.connected:
            return True

        # 尝试多个可能的路径，使用正确的文件名（包含fuzzlm_telemetry_前缀）
        possible_paths = self._get_possible_telemetry_paths(self.file_name)

        for attempt_path in possible_paths:
            logger.debug(f"Trying telemetry path: {attempt_path}")

            if Path(attempt_path).exists():
                logger.info(f"Found telemetry file at: {attempt_path}")
                self.shm_path = Path(attempt_path)
                break
        else:
            # 如果所有路径都失败，记录详细错误信息
            logger.error(
                f"❌ Shared memory file not found for stream '{self.stream_name}' "
                f"(looking for file: {self.file_name})."
            )
            logger.error("Tried the following locations:")
            for path in possible_paths:
                logger.error(f"  - {path}")
            logger.error(
                "This indicates Python and Rust are using different file names or paths."
            )
            logger.error(
                "Make sure:\n"
                "  1. Fuzzer is started before initializing TelemetryReader\n"
                "  2. The stream name matches what Rust is using (e.g., 'champion_main', 'shadow_123')\n"
                "  3. Rust creates files as: fuzzlm_telemetry_{stream_name}"
            )
            self.health_status.connected = False
            return False

        try:
            # 打开共享内存文件
            self.file_handle = open(self.shm_path, "r+b")

            # 创建内存映射
            file_size = self.shm_path.stat().st_size
            if file_size == 0:
                # 如果文件为空, 扩展到最小大小
                min_size = self.METADATA_SIZE + (1024 * 1024)  # 1MB数据区域
                self.file_handle.truncate(min_size)
                file_size = min_size

            self.mmap_obj = mmap.mmap(
                self.file_handle.fileno(),
                file_size,
                access=mmap.ACCESS_READ,
            )

            # 验证魔数和版本
            magic = struct.unpack("<I", self.mmap_obj[:4])[0]
            version = struct.unpack("<I", self.mmap_obj[4:8])[0]

            if magic != self.MAGIC_NUMBER:
                logger.warning(
                    f"Invalid magic number: {magic:#x}, expected: {self.MAGIC_NUMBER:#x}"
                )
                # 对于测试环境, 允许继续

            if version != self.VERSION:
                logger.warning(f"Version mismatch: {version}, expected: {self.VERSION}")

            self.connected = True
            self.health_status.connected = True
            logger.info(
                f"Connected to shared memory: {self.shm_path} (protobuf v{version})"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to connect to shared memory: {e}")
            self.disconnect()
            return False

    def disconnect(self) -> None:
        """断开共享内存连接"""
        if self.mmap_obj:
            self.mmap_obj.close()
            self.mmap_obj = None

        if self.file_handle:
            self.file_handle.close()
            self.file_handle = None

        self.connected = False
        self.health_status.connected = False
        logger.info("Disconnected from shared memory")

    async def read_entry(self, timeout: float = 0.01) -> dict[str, Any] | None:
        """读取单个遥测条目(非阻塞)

        Args:
            timeout: 超时时间(秒)

        Returns:
            遥测条目字典, 如果没有数据返回None

        """
        if not self.connected:
            return None

        try:
            # 尝试读取protobuf格式的数据
            entry = self._read_protobuf_entry()
            if entry:
                # 验证条目
                if self._validate_entry(entry):
                    # 转换为字典并更新健康状态
                    result = self._convert_protobuf_to_dict(entry)
                    self.health_status.last_data_time = time.time()
                    return result
                else:
                    logger.debug("Entry validation failed, skipping")

            # 如果没有真实数据，直接返回None
            # 不生成任何模拟数据，确保测试的真实性
            await asyncio.sleep(timeout)
            return None

        except Exception as e:
            logger.error(f"Error reading telemetry entry: {e}")
            self.validation_stats.corrupted_entries += 1
            return None

    def _read_protobuf_entry(self) -> TelemetryEntry | None:
        """从共享内存读取protobuf格式的遥测条目 - 支持环形缓冲区回绕"""
        if not self.mmap_obj:
            return None

        try:
            # 读取元数据获取序列号
            seq_num_before = struct.unpack("<Q", self.mmap_obj[16:24])[0]

            # 获取当前写位置
            write_pos = struct.unpack("<Q", self.mmap_obj[8:16])[0]

            # 获取数据区域大小
            data_size = len(self.mmap_obj) - self.METADATA_SIZE

            # 计算缓冲区利用率
            if data_size > 0:
                self.health_status.buffer_utilization = write_pos / data_size

            # 检查是否有足够的数据（考虑环形缓冲区）
            available_data = (
                write_pos
                if write_pos >= self.read_position
                else (data_size - self.read_position + write_pos)
            )
            if available_data < 4:
                return None

            # 读取长度字段（4字节），处理可能的回绕
            length_bytes = bytearray(4)
            for i in range(4):
                pos = (self.read_position + i) % data_size
                actual_pos = self.METADATA_SIZE + pos
                length_bytes[i] = self.mmap_obj[actual_pos]

            entry_length = struct.unpack("<I", length_bytes)[0]

            # 验证长度合理性
            if entry_length <= 0 or entry_length > self.MAX_ENTRY_SIZE:
                logger.debug(f"无效的条目长度: {entry_length}")
                self.validation_stats.corrupted_entries += 1
                return None

            # 检查完整数据可用性
            if available_data < 4 + entry_length:
                return None

            # 读取protobuf数据，处理可能的回绕
            entry_data = bytearray(entry_length)
            for i in range(entry_length):
                pos = (self.read_position + 4 + i) % data_size
                actual_pos = self.METADATA_SIZE + pos
                entry_data[i] = self.mmap_obj[actual_pos]

            # 再次检查序列号，确保读取期间没有被覆盖
            seq_num_after = struct.unpack("<Q", self.mmap_obj[16:24])[0]

            # 验证序列号
            if self.validation_stats.last_sequence_number >= 0:
                expected_seq = self.validation_stats.last_sequence_number + 1
                if seq_num_after != expected_seq:
                    gap = seq_num_after - expected_seq
                    if gap > 0:
                        # 序列号间隙
                        self.validation_stats.sequence_gaps.append(
                            (expected_seq, seq_num_after)
                        )
                        if len(self.validation_stats.sequence_gaps) > 100:
                            self.validation_stats.sequence_gaps = (
                                self.validation_stats.sequence_gaps[-100:]
                            )
                    else:
                        # 序列号乱序
                        self.validation_stats.out_of_order_count += 1

            self.validation_stats.last_sequence_number = seq_num_after

            if seq_num_after != seq_num_before:
                logger.debug("检测到读取期间数据变更，跳过此条目")
                return None

            # 解析protobuf消息
            entry = TelemetryEntry()
            try:
                entry.ParseFromString(bytes(entry_data))
                self.validation_stats.entries_read += 1
                self.validation_stats.bytes_read += entry_length + 4
            except Exception as e:
                logger.debug(f"Protobuf解析失败: {e}")
                self.validation_stats.invalid_protobuf += 1
                return None

            # 基本数据验证
            if entry.timestamp_ns == 0:
                logger.debug("无效的时间戳，跳过条目")
                self.validation_stats.missing_fields += 1
                return None

            # 验证instance_id非空
            if not entry.instance_id:
                logger.debug("缺少instance_id，跳过条目")
                self.validation_stats.missing_fields += 1
                return None

            # 更新读取位置（处理回绕）
            self.read_position = (self.read_position + 4 + entry_length) % data_size

            # 定期更新数据率
            if self.validation_stats.entries_read % 100 == 0:
                self.validation_stats.update_rates()

            return entry

        except Exception as e:
            logger.debug(f"读取protobuf条目错误: {e}")
            self.validation_stats.corrupted_entries += 1
            return None

    def _validate_entry(self, entry: TelemetryEntry) -> bool:
        """验证遥测条目的完整性和有效性

        Args:
            entry: 要验证的protobuf条目

        Returns:
            条目是否有效
        """
        # 时间戳验证
        current_time_ns = time.time() * 1_000_000_000
        timestamp_seconds = entry.timestamp_ns / 1_000_000_000.0

        # 检查未来时间戳
        if entry.timestamp_ns > current_time_ns + (
            self.MAX_TIMESTAMP_DRIFT * 1_000_000_000
        ):
            logger.warning(f"检测到未来时间戳: {timestamp_seconds:.3f}")
            self.validation_stats.future_timestamps += 1
            return False

        # 检查过期时间戳
        if entry.timestamp_ns < current_time_ns - (
            self.MAX_TIMESTAMP_DRIFT * 1_000_000_000
        ):
            logger.debug(f"检测到过期时间戳: {timestamp_seconds:.3f}")
            self.validation_stats.stale_timestamps += 1
            # 过期时间戳仍然允许，只是记录

        # 检查时间戳跳变
        if self.validation_stats.last_timestamp > 0:
            time_diff = abs(timestamp_seconds - self.validation_stats.last_timestamp)
            if time_diff > 60:  # 1分钟以上的跳变
                logger.debug(f"检测到时间戳跳变: {time_diff:.1f}秒")
                self.validation_stats.timestamp_jumps += 1

        self.validation_stats.last_timestamp = timestamp_seconds

        # 验证数据类型有效
        if entry.data_type < 0 or entry.data_type > 10:  # 假设有效范围
            logger.warning(f"无效的数据类型: {entry.data_type}")
            return False

        # 验证payload存在
        payload_type = entry.WhichOneof("payload")
        if not payload_type:
            logger.debug("条目缺少payload")
            self.validation_stats.missing_fields += 1
            return False

        # 验证具体数据字段
        if payload_type == "execution_stats":
            stats = entry.execution_stats
            if stats.executions < 0 or stats.exec_per_sec < 0:
                logger.debug("执行统计数据无效")
                return False
        elif payload_type == "coverage_hit":
            hit = entry.coverage_hit
            if hit.edge_id < 0 or hit.hit_count < 0:
                logger.debug("覆盖率数据无效")
                return False
        elif payload_type == "crash_found":
            crash = entry.crash_found
            if not crash.crash_type or crash.signal < 0:
                logger.debug("崩溃数据无效")
                return False

        return True

    def _convert_protobuf_to_dict(self, entry: TelemetryEntry) -> dict[str, Any]:
        """将protobuf条目转换为字典格式"""
        # 基础信息
        result = {
            "type": TelemetryDataType.Name(entry.data_type).lower(),
            "instance_id": entry.instance_id,
            "timestamp": entry.timestamp_ns / 1_000_000_000.0,  # 转换为秒
            "data_type": entry.data_type,  # 添加数值类型供处理器使用
        }

        # 根据数据类型解析payload - 保持嵌套结构以兼容TelemetryAggregator
        payload_type = entry.WhichOneof("payload")
        if payload_type == "execution_stats":
            stats = entry.execution_stats
            result["execution_stats"] = {
                "executions": stats.executions,
                "exec_per_sec": stats.exec_per_sec,
                "corpus_size": stats.corpus_size,
                "crashes": stats.crashes,
                "coverage_edges": stats.coverage_edges,  # 添加覆盖率边数字段
            }
        elif payload_type == "coverage_hit":
            hit = entry.coverage_hit
            result["coverage_hit"] = {
                "edge_id": hit.edge_id,
                "hit_count": hit.hit_count,
                "is_new": hit.is_new,
            }
        elif payload_type == "crash_found":
            crash = entry.crash_found
            result["crash_found"] = {
                "crash_type": crash.crash_type,
                "input_hash": crash.input_hash,
                "signal": crash.signal,
            }
        elif payload_type == "corpus_grow":
            grow = entry.corpus_grow
            result["corpus_grow"] = {
                "new_inputs": grow.new_inputs,
                "total_size": grow.total_size,
                "avg_length": grow.avg_length,
            }
        elif payload_type == "mutator_stats":
            stats = entry.mutator_stats
            result["mutator_stats"] = {
                "mutator_id": stats.mutator_id,
                "usage_count": stats.usage_count,
                "success_rate": stats.success_rate,
            }

        return result

    def _get_possible_telemetry_paths(self, file_name: str) -> list[str]:
        """获取所有可能的遥测文件路径

        Args:
            file_name: 文件名（已包含fuzzlm_telemetry_前缀）

        Returns:
            可能的文件路径列表，按优先级排序
        """
        import os

        paths = []

        # 1. 当前配置的路径
        if hasattr(self, "shm_path") and self.shm_path:
            paths.append(str(self.shm_path))

        # 2. 基于当前base_path的路径
        if hasattr(self, "base_path") and self.base_path:
            paths.append(os.path.join(self.base_path, file_name))

        # 3. fuzzing-engine目录下的runtime/temp (Rust进程工作目录)
        current_dir = os.getcwd()
        if "fuzzlm-agent" in current_dir:
            parts = current_dir.split(os.sep)
            for i, part in enumerate(parts):
                if part == "fuzzlm-agent":
                    project_root = os.sep.join(parts[: i + 1])
                    fuzzing_engine_path = os.path.join(
                        project_root,
                        "fuzzlm_agent",
                        "fuzzing-engine",
                        "runtime",
                        "temp",
                        file_name,
                    )
                    paths.append(fuzzing_engine_path)
                    break

        # 4. 项目根目录下的runtime/temp
        if "fuzzlm-agent" in current_dir:
            parts = current_dir.split(os.sep)
            for i, part in enumerate(parts):
                if part == "fuzzlm-agent":
                    project_root = os.sep.join(parts[: i + 1])
                    root_runtime_path = os.path.join(
                        project_root, "runtime", "temp", file_name
                    )
                    paths.append(root_runtime_path)
                    break

        # 5. 环境变量指定的路径
        shm_dir = os.environ.get("FUZZLM_SHM_DIR")
        if shm_dir:
            paths.append(os.path.join(shm_dir, file_name))

        # 6. 当前目录下的runtime/temp
        paths.append(os.path.join(current_dir, "runtime", "temp", file_name))

        # 去重并保持顺序
        seen = set()
        unique_paths = []
        for path in paths:
            if path not in seen:
                seen.add(path)
                unique_paths.append(path)

        return unique_paths

    async def read_batch(self, max_entries: int = 100) -> list[dict[str, Any]]:
        """批量读取遥测条目

        Args:
            max_entries: 最大读取条目数

        Returns:
            遥测条目列表

        """
        entries = []
        for _ in range(max_entries):
            entry = await self.read_entry(timeout=0.001)
            if entry is None:
                break
            entries.append(entry)
        return entries

    def _create_test_shm(self) -> None:
        """创建测试用的共享内存文件"""
        try:
            # 创建共享内存目录(如果不存在)
            self.shm_path.parent.mkdir(parents=True, exist_ok=True)

            # 创建并初始化共享内存文件
            with open(self.shm_path, "wb") as f:
                # 写入头部 - protobuf版本
                header = struct.pack(
                    "<IIII",  # 魔数、版本、保留、保留
                    self.MAGIC_NUMBER,
                    self.VERSION,
                    0,  # 保留字段
                    0,  # 保留字段
                )
                f.write(header)

                # 预分配空间
                f.write(b"\x00" * (1024 * 1024))  # 1MB数据区域

            logger.info(f"Created test shared memory file: {self.shm_path} (protobuf)")

        except Exception as e:
            logger.error(f"Failed to create test shared memory: {e}")
            raise

    def get_stats(self) -> dict[str, Any]:
        """获取统计信息

        Returns:
            统计信息字典

        """
        # 检查健康状态
        is_healthy, issues = self.health_status.check_health()

        return {
            "connected": self.connected,
            "stream_name": self.stream_name,
            "shm_path": str(self.shm_path),
            "read_position": self.read_position,
            "format": "protobuf",
            "version": self.VERSION,
            "health": {
                "is_healthy": is_healthy,
                "issues": issues,
                "last_data_time": self.health_status.last_data_time,
                "data_staleness_seconds": self.health_status.data_staleness_seconds,
                "buffer_utilization": self.health_status.buffer_utilization,
            },
            "validation": {
                "entries_read": self.validation_stats.entries_read,
                "corrupted_entries": self.validation_stats.corrupted_entries,
                "invalid_protobuf": self.validation_stats.invalid_protobuf,
                "missing_fields": self.validation_stats.missing_fields,
                "sequence_gaps": len(self.validation_stats.sequence_gaps),
                "out_of_order_count": self.validation_stats.out_of_order_count,
                "future_timestamps": self.validation_stats.future_timestamps,
                "stale_timestamps": self.validation_stats.stale_timestamps,
                "timestamp_jumps": self.validation_stats.timestamp_jumps,
                "data_rate_bps": self.validation_stats.data_rate_bps,
                "entry_rate_eps": self.validation_stats.entry_rate_eps,
            },
        }

    def get_health_report(self) -> str:
        """生成健康报告字符串"""
        stats = self.get_stats()
        health = stats["health"]
        validation = stats["validation"]

        report = f"""
=== Telemetry Health Report ===
Stream: {self.stream_name}
Connected: {self.connected}
Health Status: {'✅ Healthy' if health['is_healthy'] else '❌ Unhealthy'}

Issues:
{chr(10).join(f"  - {issue}" for issue in health['issues']) if health['issues'] else "  None"}

Validation Statistics:
  - Entries Read: {validation['entries_read']:,}
  - Corrupted Entries: {validation['corrupted_entries']}
  - Invalid Protobuf: {validation['invalid_protobuf']}
  - Missing Fields: {validation['missing_fields']}
  - Sequence Gaps: {validation['sequence_gaps']}
  - Out of Order: {validation['out_of_order_count']}
  - Future Timestamps: {validation['future_timestamps']}
  - Stale Timestamps: {validation['stale_timestamps']}
  - Timestamp Jumps: {validation['timestamp_jumps']}

Performance:
  - Data Rate: {validation['data_rate_bps']:.2f} bytes/sec
  - Entry Rate: {validation['entry_rate_eps']:.2f} entries/sec
  - Buffer Utilization: {health['buffer_utilization']:.1%}
  - Data Staleness: {health['data_staleness_seconds']:.1f} seconds
"""
        return report


class TelemetryReader:
    """遥测读取器 - 提供给Phase 3和Phase 4使用的高级接口

    封装了TelemetryDataPlane, 提供更简单的API。
    """

    def __init__(self, stream_name: str = "champion_main"):
        """初始化遥测读取器

        Args:
            stream_name: 共享内存流名称（不包含fuzzlm_telemetry_前缀）

        """
        self.data_plane = TelemetryDataPlane(stream_name)
        self.connected = False

    async def connect(self, max_retries: int = 20, retry_delay: float = 1.0) -> bool:
        """连接到遥测流，支持robust重试机制

        Args:
            max_retries: 最大重试次数
            retry_delay: 重试间隔(秒)

        Returns:
            是否成功连接
        """
        for attempt in range(max_retries + 1):
            self.connected = self.data_plane.connect()
            if self.connected:
                logger.info(
                    f"Successfully connected to telemetry stream: {self.data_plane.stream_name}"
                )
                return True

            if attempt < max_retries:
                # 使用指数退避，但有最大限制
                delay = min(retry_delay * (1.2**attempt), 3.0)
                logger.debug(
                    f"Connection attempt {attempt + 1}/{max_retries + 1} failed, retrying in {delay:.1f}s..."
                )
                await asyncio.sleep(delay)
            else:
                logger.error(
                    f"Failed to connect to telemetry stream after {max_retries + 1} attempts"
                )

        return False

    async def disconnect(self) -> None:
        """断开连接"""
        self.data_plane.disconnect()
        self.connected = False

    async def read_entry(self, timeout: float = 0.01) -> dict[str, Any] | None:
        """读取单个遥测条目"""
        if not self.connected:
            return None
        return await self.data_plane.read_entry(timeout)

    async def read_batch(self, max_entries: int = 100) -> list[dict[str, Any]]:
        """批量读取遥测条目"""
        if not self.connected:
            return []
        return await self.data_plane.read_batch(max_entries)


# 辅助函数
async def create_telemetry_reader(
    stream_name: str = "champion_main",
) -> TelemetryReader:
    """创建并连接遥测读取器

    Args:
        stream_name: 共享内存流名称（不包含fuzzlm_telemetry_前缀）

    Returns:
        已连接的TelemetryReader实例

    """
    reader = TelemetryReader(stream_name)
    await reader.connect()
    return reader


class TelemetryHealthMonitor:
    """遥测系统健康监控器

    提供集中的健康监控和异常检测功能
    """

    def __init__(self, check_interval: float = 30.0):
        """初始化健康监控器

        Args:
            check_interval: 健康检查间隔（秒）
        """
        self.check_interval = check_interval
        self.readers: dict[str, TelemetryReader] = {}
        self.last_check_time = time.time()
        self.health_history: deque[dict[str, Any]] = deque(maxlen=100)

        # 异常检测阈值
        self.anomaly_thresholds = {
            "data_loss_rate": 0.05,  # 5%数据丢失
            "corruption_rate": 0.01,  # 1%损坏率
            "stale_data_seconds": 60,  # 60秒无数据
            "sequence_gap_size": 1000,  # 序列号间隙大小
            "buffer_utilization": 0.9,  # 90%缓冲区利用率
        }

    def add_reader(self, name: str, reader: TelemetryReader) -> None:
        """添加要监控的读取器"""
        self.readers[name] = reader

    def remove_reader(self, name: str) -> None:
        """移除读取器"""
        self.readers.pop(name, None)

    async def check_health(self) -> dict[str, Any]:
        """执行健康检查

        Returns:
            健康检查结果
        """
        current_time = time.time()
        results = {
            "timestamp": current_time,
            "readers": {},
            "overall_health": True,
            "anomalies": [],
        }

        for name, reader in self.readers.items():
            if not reader.connected:
                results["readers"][name] = {
                    "connected": False,
                    "healthy": False,
                    "issues": ["未连接"],
                }
                results["overall_health"] = False
                continue

            # 获取读取器统计
            stats = reader.data_plane.get_stats()
            health = stats["health"]
            validation = stats["validation"]

            # 检测异常
            reader_anomalies = self._detect_anomalies(name, stats)
            results["anomalies"].extend(reader_anomalies)

            # 记录读取器健康状态
            results["readers"][name] = {
                "connected": True,
                "healthy": health["is_healthy"],
                "issues": health["issues"],
                "stats": {
                    "entries_read": validation["entries_read"],
                    "data_rate_bps": validation["data_rate_bps"],
                    "buffer_utilization": health["buffer_utilization"],
                    "data_staleness": health["data_staleness_seconds"],
                },
            }

            if not health["is_healthy"]:
                results["overall_health"] = False

        # 记录历史
        self.health_history.append(results)
        self.last_check_time = current_time

        return results

    def _detect_anomalies(
        self, reader_name: str, stats: dict[str, Any]
    ) -> list[dict[str, Any]]:
        """检测数据异常

        Args:
            reader_name: 读取器名称
            stats: 统计信息

        Returns:
            检测到的异常列表
        """
        anomalies = []
        health = stats["health"]
        validation = stats["validation"]

        # 检查数据丢失率
        if validation["entries_read"] > 0:
            loss_rate = validation["sequence_gaps"] / validation["entries_read"]
            if loss_rate > self.anomaly_thresholds["data_loss_rate"]:
                anomalies.append(
                    {
                        "reader": reader_name,
                        "type": "high_data_loss",
                        "severity": "warning",
                        "value": loss_rate,
                        "threshold": self.anomaly_thresholds["data_loss_rate"],
                        "message": f"数据丢失率过高: {loss_rate:.2%}",
                    }
                )

        # 检查数据损坏率
        if validation["entries_read"] > 0:
            corruption_rate = (
                validation["corrupted_entries"] + validation["invalid_protobuf"]
            ) / validation["entries_read"]

            if corruption_rate > self.anomaly_thresholds["corruption_rate"]:
                anomalies.append(
                    {
                        "reader": reader_name,
                        "type": "high_corruption",
                        "severity": "error",
                        "value": corruption_rate,
                        "threshold": self.anomaly_thresholds["corruption_rate"],
                        "message": f"数据损坏率过高: {corruption_rate:.2%}",
                    }
                )

        # 检查数据新鲜度
        if (
            health["data_staleness_seconds"]
            > self.anomaly_thresholds["stale_data_seconds"]
        ):
            anomalies.append(
                {
                    "reader": reader_name,
                    "type": "stale_data",
                    "severity": "warning",
                    "value": health["data_staleness_seconds"],
                    "threshold": self.anomaly_thresholds["stale_data_seconds"],
                    "message": f"数据过期: {health['data_staleness_seconds']:.1f}秒无更新",
                }
            )

        # 检查缓冲区利用率
        if health["buffer_utilization"] > self.anomaly_thresholds["buffer_utilization"]:
            anomalies.append(
                {
                    "reader": reader_name,
                    "type": "high_buffer_usage",
                    "severity": "warning",
                    "value": health["buffer_utilization"],
                    "threshold": self.anomaly_thresholds["buffer_utilization"],
                    "message": f"缓冲区利用率过高: {health['buffer_utilization']:.1%}",
                }
            )

        # 检查极端值
        if validation["future_timestamps"] > 10:
            anomalies.append(
                {
                    "reader": reader_name,
                    "type": "future_timestamps",
                    "severity": "error",
                    "value": validation["future_timestamps"],
                    "message": f"检测到{validation['future_timestamps']}个未来时间戳",
                }
            )

        return anomalies

    async def monitor_loop(self) -> None:
        """持续监控循环"""
        logger.info(f"启动遥测健康监控，检查间隔: {self.check_interval}秒")

        while True:
            try:
                # 执行健康检查
                results = await self.check_health()

                # 记录结果
                if not results["overall_health"]:
                    logger.warning("遥测系统健康检查失败")
                    for reader_name, reader_info in results["readers"].items():
                        if not reader_info["healthy"]:
                            logger.warning(
                                f"读取器 {reader_name} 不健康: {reader_info['issues']}"
                            )

                # 记录异常
                for anomaly in results["anomalies"]:
                    if anomaly["severity"] == "error":
                        logger.error(f"检测到异常: {anomaly['message']}")
                    else:
                        logger.warning(f"检测到异常: {anomaly['message']}")

            except Exception as e:
                logger.error(f"健康监控错误: {e}")

            # 等待下次检查
            await asyncio.sleep(self.check_interval)

    def get_health_summary(self) -> dict[str, Any]:
        """获取健康摘要

        Returns:
            最近的健康状态摘要
        """
        if not self.health_history:
            return {
                "status": "unknown",
                "message": "尚无健康检查数据",
            }

        latest = self.health_history[-1]

        # 统计最近的异常
        recent_anomalies = []
        for record in list(self.health_history)[-10:]:
            recent_anomalies.extend(record["anomalies"])

        # 按类型分组异常
        anomaly_counts = {}
        for anomaly in recent_anomalies:
            anomaly_type = anomaly["type"]
            anomaly_counts[anomaly_type] = anomaly_counts.get(anomaly_type, 0) + 1

        return {
            "status": "healthy" if latest["overall_health"] else "unhealthy",
            "timestamp": latest["timestamp"],
            "readers": latest["readers"],
            "anomaly_summary": anomaly_counts,
            "recent_anomalies": recent_anomalies[-5:],  # 最近5个异常
        }


# 向后兼容性工具类
class TelemetryDataTypeCompat:
    """保持向后兼容性的数据类型枚举工具"""

    @classmethod
    def from_protobuf(cls, pb_type: int) -> str:
        """从protobuf类型转换为字符串"""
        return str(TelemetryDataType.Name(pb_type)).lower()

    @classmethod
    def to_protobuf(cls, type_str: str) -> int:
        """从字符串转换为protobuf类型"""
        return int(getattr(TelemetryDataType, type_str.upper()))
