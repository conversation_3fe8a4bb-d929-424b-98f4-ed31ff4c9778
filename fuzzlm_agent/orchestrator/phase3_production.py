"""Phase 3: Production Run with Dynamic Shadow Process Support
=========================================================

SOTA implementation of Phase 3 production run using concurrent tasks for real-time
responsiveness and dynamic shadow process creation as per research requirements
in docs/workflow.md.

Architecture:
- Telemetry collection task: Continuous non-blocking data gathering
- LLM analysis task: Periodic state analysis every 10 minutes
- Anomaly detection task: Real-time anomaly monitoring with immediate response
- Shadow process manager: Dynamic shadow process creation and management

This is a research prototype focused on functional completeness while avoiding
over-engineering. Uses standard Python asyncio patterns for simplicity.

Key Improvements:
- Robust error recovery with exponential backoff
- Task health monitoring and automatic restart
- Structured concurrent task management
- Enhanced type safety with TypedDict
- Modular function design for maintainability
"""

from __future__ import annotations

import asyncio
import logging
from collections.abc import Awaitable
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import (
    TYPE_CHECKING,
    Any,
    Callable,
)

import json_repair

from fuzzlm_agent.core import (
    PerformanceMetrics,
    TelemetryAnalyzer,
)
from fuzzlm_agent.core.statistical_analysis import TelemetryAggregator
from fuzzlm_agent.orchestrator.constants import (
    ANOMALY_CHECK_INTERVAL_SECONDS,
    LLM_ANALYSIS_INTERVAL_MINUTES,
    MAX_TASK_RESTART_ATTEMPTS,
    TASK_HEALTH_CHECK_INTERVAL,
    TELEMETRY_BATCH_SIZE,
    TELEMETRY_COLLECTION_INTERVAL,
)
from fuzzlm_agent.orchestrator.shadow_process_manager import ShadowProcessManager
from fuzzlm_agent.orchestrator.types import TaskStatus
from fuzzlm_agent.prompts import PromptManager

if TYPE_CHECKING:
    from fuzzlm_agent.infrastructure.litellm_client import LiteLLMClient
    from fuzzlm_agent.infrastructure.runtime_client import RuntimeClient
    from fuzzlm_agent.infrastructure.shared_memory import TelemetryReader
    from fuzzlm_agent.orchestrator.campaign_orchestrator import CampaignContext

logger = logging.getLogger(__name__)

# Initialize prompt manager (global instance for performance)
_prompt_manager = PromptManager()


class ProductionPhaseError(Exception):
    """Phase 3 production run specific errors"""


class TaskFailureError(ProductionPhaseError):
    """Raised when a monitoring task fails critically"""


class TelemetryError(ProductionPhaseError):
    """Raised when telemetry collection fails"""


class AnalysisStage(Enum):
    """Fuzzing analysis stages"""

    EXPLORATION = "exploration"
    EXPLOITATION = "exploitation"
    STAGNATION = "stagnation"
    UNKNOWN = "unknown"


@dataclass
class StateAnalysis:
    """LLM's analysis of fuzzing state."""

    timestamp: datetime
    current_stage: AnalysisStage
    confidence: float
    bottlenecks: list[str] = field(default_factory=list)
    recommendations: list[str] = field(default_factory=list)
    needs_adjustment: bool = False
    suggested_actions: list[str] = field(default_factory=list)
    raw_response: str = ""
    error: str | None = None


async def phase3_production_run(
    ctx: CampaignContext,
    runtime_client: RuntimeClient,
    llm_client: LiteLLMClient,
    duration_hours: float,
) -> CampaignContext:
    """Phase 3: Production run with adaptive state perception using concurrent tasks.

    Implements the real-time responsiveness requirements from workflow.md:
    - "每10分钟将收集到的近期原始数据流发送给LLM" (Send data to LLM every 10 minutes)
    - Real-time anomaly detection and response
    - Non-blocking telemetry collection

    Args:
        ctx: Campaign context with strategy
        runtime_client: Runtime client for fuzzer control
        llm_client: LLM client for state analysis
        duration_hours: Duration of production run in hours

    Returns:
        Updated campaign context with monitoring results

    Note:
        TelemetryReader is initialized after fuzzer starts to ensure correct
        shared memory file name matching with Rust fuzzing engine.
    """
    logger.info(
        f"Phase 3: Starting production run for {duration_hours} hours with concurrent architecture"
    )

    # Ensure metadata field exists
    if not hasattr(ctx, "metadata"):
        ctx.metadata = {}

    # Validate prerequisites
    if ctx.strategy is None:
        raise ValueError("Strategy not available for production run")

    # telemetry_reader will be initialized after fuzzer starts
    # to ensure correct shared memory file name matching

    # Initialize resource tracking
    ctx.resource_usage["phase3_start"] = datetime.now(timezone.utc).isoformat()

    # Start champion fuzzer with retry
    fuzzer_id = await _start_champion_fuzzer_with_retry(
        runtime_client, ctx.strategy, ctx.target_path
    )

    if not fuzzer_id:
        error_msg = "Failed to start champion fuzzer after retries"
        logger.error(error_msg)
        ctx.metadata["phase3_error"] = error_msg
        raise ProductionPhaseError(error_msg)

    ctx.metadata["champion_fuzzer_id"] = fuzzer_id
    logger.info(f"Champion fuzzer started with ID: {fuzzer_id}")

    # Now initialize TelemetryReader with correct instance_id
    # Rust creates file: fuzzlm_telemetry_{fuzzer_id}
    from fuzzlm_agent.infrastructure.shared_memory import TelemetryReader

    try:
        telemetry_reader = TelemetryReader(f"fuzzlm_telemetry_{fuzzer_id}")
        # 增加等待时间和重试次数，以应对Rust初始化时间
        connected = await telemetry_reader.connect(max_retries=25, retry_delay=2.0)
        if not connected:
            error_msg = (
                f"Failed to connect to telemetry stream: fuzzlm_telemetry_{fuzzer_id}"
            )
            logger.error(error_msg)

            # 尝试查找实际创建的文件
            import subprocess

            try:
                result = subprocess.run(
                    [
                        "find",
                        "/home/<USER>/git/fuzzlm-agent",
                        "-name",
                        f"*{fuzzer_id}*",
                        "-type",
                        "f",
                    ],
                    capture_output=True,
                    text=True,
                    timeout=5,
                )
                if result.stdout.strip():
                    logger.info(f"Found related files: {result.stdout.strip()}")
                else:
                    logger.error(f"No telemetry files found for fuzzer {fuzzer_id}")
            except Exception as find_error:
                logger.debug(f"File search failed: {find_error}")

            ctx.metadata["phase3_error"] = error_msg
            raise ProductionPhaseError(error_msg)
        logger.info(
            f"Telemetry reader successfully connected to: fuzzlm_telemetry_{fuzzer_id}"
        )
    except Exception as e:
        error_msg = f"Failed to initialize telemetry reader for {fuzzer_id}: {e}"
        logger.error(error_msg)
        ctx.metadata["phase3_error"] = error_msg
        raise ProductionPhaseError(error_msg) from e

    # Create production run manager
    manager = ProductionRunManager(
        ctx=ctx,
        runtime_client=runtime_client,
        llm_client=llm_client,
        telemetry_reader=telemetry_reader,
        fuzzer_id=fuzzer_id,
        duration_hours=duration_hours,
    )

    # Run production monitoring
    await manager.run()

    # Update context with results
    ctx = manager.get_updated_context()

    return ctx


class ProductionRunManager:
    """Manages the entire Phase 3 production run with concurrent tasks

    Encapsulates all production run state and provides centralized
    task management with health monitoring and error recovery.
    """

    def __init__(
        self,
        ctx: CampaignContext,
        runtime_client: RuntimeClient,
        llm_client: LiteLLMClient,
        telemetry_reader: TelemetryReader,
        fuzzer_id: str,
        duration_hours: float,
    ):
        self.ctx = ctx
        self.runtime_client = runtime_client
        self.llm_client = llm_client
        self.telemetry_reader = telemetry_reader
        self.fuzzer_id = fuzzer_id
        self.duration_hours = duration_hours

        # Shared state with thread-safe access
        self.state_analyses: list[StateAnalysis] = []
        self.performance_anomalies: list[str] = []
        self.needs_adjustment = False
        self.telemetry_analyzer = TelemetryAnalyzer()

        # Synchronization primitives
        self.data_lock = asyncio.Lock()
        self.stop_event = asyncio.Event()

        # Task management
        self.tasks: dict[str, asyncio.Task[Any]] = {}
        self.task_status: dict[str, TaskStatus] = {}

        # Timing control
        self.start_time = datetime.now(timezone.utc)
        self.end_time = self.start_time + timedelta(hours=duration_hours)

        # Shadow process manager with heartbeat callback
        self.shadow_manager = ShadowProcessManager(
            runtime_client=runtime_client,
            telemetry_collector=telemetry_reader,
            config=getattr(ctx, "config", {}),
            champion_id=fuzzer_id,
        )

        # Add heartbeat callback to shadow manager
        self.shadow_manager._heartbeat_callback = self._update_task_heartbeat

    async def _shadow_manager_wrapper(self) -> None:
        """Wrapper for shadow manager to ensure proper logging"""
        logger.info("Starting shadow manager with heartbeat support")
        try:
            await self.shadow_manager.start()
        except Exception as e:
            logger.error(f"Shadow manager failed: {e}")
            raise
        finally:
            logger.info("Shadow manager stopped")

    async def run(self) -> None:
        """Run the production monitoring with all tasks"""
        try:
            # Start all monitoring tasks
            await self._start_all_tasks()

            # Monitor task health and progress
            await self._monitor_tasks()

        finally:
            # Ensure cleanup happens
            await self._cleanup()

    async def _start_all_tasks(self) -> None:
        """Start all monitoring tasks with health tracking"""
        # Define tasks to start
        task_definitions = [
            ("telemetry_collection", self._telemetry_collection_task),
            ("llm_analysis", self._llm_analysis_task),
            ("anomaly_detection", self._anomaly_detection_task),
            ("shadow_manager", self._shadow_manager_wrapper),
        ]

        # Start each task with health monitoring
        for name, coro_func in task_definitions:
            task = asyncio.create_task(
                self._run_task_with_health_monitoring(name, coro_func), name=name
            )
            self.tasks[name] = task
            self.task_status[name] = TaskStatus(
                name=name,
                state="running",
                last_heartbeat=datetime.now(timezone.utc),
                restart_count=0,
                error=None,
            )

        logger.info(f"Started {len(self.tasks)} monitoring tasks")

    async def _run_task_with_health_monitoring(
        self,
        task_name: str,
        coro_func: Callable[[], Awaitable[None]],
    ) -> None:
        """Run a task with health monitoring and automatic restart"""
        restart_count = 0

        while (
            restart_count < MAX_TASK_RESTART_ATTEMPTS and not self.stop_event.is_set()
        ):
            try:
                # Update task status
                self.task_status[task_name] = TaskStatus(
                    name=task_name,
                    state="running",
                    last_heartbeat=datetime.now(timezone.utc),
                    restart_count=restart_count,
                    error=None,
                )

                # Run the actual task
                await coro_func()

                # Task completed normally
                break

            except asyncio.CancelledError:
                logger.info(f"Task {task_name} cancelled")
                raise

            except Exception as e:
                restart_count += 1
                error_msg = f"Task {task_name} failed: {e}"
                logger.error(error_msg, exc_info=True)

                # Update status
                self.task_status[task_name] = TaskStatus(
                    name=task_name,
                    state="failed",
                    last_heartbeat=datetime.now(timezone.utc),
                    restart_count=restart_count,
                    error=str(e),
                )

                if restart_count < MAX_TASK_RESTART_ATTEMPTS:
                    # Exponential backoff before restart
                    wait_time = 2**restart_count
                    logger.info(
                        f"Restarting {task_name} in {wait_time}s (attempt {restart_count})"
                    )
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(
                        f"Task {task_name} failed permanently after {restart_count} attempts"
                    )
                    raise TaskFailureError(
                        f"{task_name} failed after {restart_count} attempts"
                    ) from e

    async def _monitor_tasks(self) -> None:
        """Monitor task health and production progress"""
        check_interval = TASK_HEALTH_CHECK_INTERVAL
        elapsed_time = 0
        health_check_count = 0

        logger.info(
            f"Starting production run monitoring for {self.duration_hours} hours"
        )

        while (
            elapsed_time < self.duration_hours * 3600 and not self.stop_event.is_set()
        ):
            # Wait for check interval
            await asyncio.sleep(check_interval)
            elapsed_time += check_interval
            health_check_count += 1

            # Log progress
            progress = (elapsed_time / (self.duration_hours * 3600)) * 100
            elapsed_hours = elapsed_time / 3600
            remaining_hours = self.duration_hours - elapsed_hours

            logger.info(
                f"Production run progress: {progress:.1f}% "
                f"({elapsed_hours:.1f}/{self.duration_hours} hours completed, "
                f"{remaining_hours:.1f} hours remaining)"
            )

            # Check task health
            task_health_summary = await self._check_task_health()

            # Log task status summary
            if health_check_count % 10 == 0:  # Every 10 checks (~5 minutes)
                logger.info(
                    f"Task health summary: "
                    f"Running: {task_health_summary['running']}, "
                    f"Failed: {task_health_summary['failed']}, "
                    f"Total restarts: {task_health_summary['total_restarts']}"
                )

            # Update shadow process status
            self.ctx.metadata["shadow_process_status"] = (
                self.shadow_manager.get_status()
            )

            # Check if we should stop early
            if datetime.now(timezone.utc) >= self.end_time:
                logger.info("Production run duration reached")
                break

        logger.info(
            f"Production run monitoring completed. "
            f"Total duration: {elapsed_time / 3600:.1f} hours, "
            f"Health checks performed: {health_check_count}"
        )

    async def _check_task_health(self) -> dict[str, int]:
        """Check health of all running tasks

        Returns:
            dict: Summary of task health status
        """
        current_time = datetime.now(timezone.utc)
        running = 0
        failed = 0
        total_restarts = 0

        for task_name, task in self.tasks.items():
            status = self.task_status.get(task_name)
            if status:
                total_restarts += status["restart_count"]

            if task.done() and not task.cancelled():
                # Task completed or failed
                try:
                    task.result()  # This will raise any exception
                except Exception as e:
                    logger.error(f"Task {task_name} has failed: {e}")
                    failed += 1
                    # Task restart is handled by health monitoring wrapper
            else:
                running += 1

            # Check heartbeat for long-running tasks
            if status and status["state"] == "running":
                time_since_heartbeat = (
                    current_time - status["last_heartbeat"]
                ).total_seconds()

                if time_since_heartbeat > 300:  # 5 minutes
                    logger.warning(
                        f"Task {task_name} hasn't updated heartbeat in {time_since_heartbeat}s"
                    )

        return {
            "running": running,
            "failed": failed,
            "total_restarts": total_restarts,
        }

    async def _cleanup(self) -> None:
        """Clean up all resources gracefully"""
        logger.info("Starting production run cleanup")

        # Signal all tasks to stop
        self.stop_event.set()

        # Stop shadow manager first
        try:
            await asyncio.wait_for(self.shadow_manager.stop(), timeout=5.0)
        except asyncio.TimeoutError:
            logger.warning("Shadow manager stop timed out")
        except Exception as e:
            logger.error(f"Error stopping shadow manager: {e}")

        # Cancel all tasks
        for task_name, task in self.tasks.items():
            if not task.done():
                logger.info(f"Cancelling task: {task_name}")
                task.cancel()

        # Wait for tasks to complete
        if self.tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self.tasks.values(), return_exceptions=True),
                    timeout=5.0,
                )
            except asyncio.TimeoutError:
                logger.warning("Some tasks did not complete gracefully")

        # Stop champion fuzzer
        try:
            logger.info("Stopping champion fuzzer")
            await self.runtime_client.stop_fuzzer(self.fuzzer_id)
        except Exception as e:
            logger.error(f"Failed to stop fuzzer: {e}")

        logger.info("Production run cleanup completed")

    def _update_task_heartbeat(self, task_name: str) -> None:
        """Update heartbeat for a specific task

        Args:
            task_name: Name of the task to update heartbeat for
        """
        if task_name in self.task_status:
            self.task_status[task_name]["last_heartbeat"] = datetime.now(timezone.utc)

    def get_updated_context(self) -> CampaignContext:
        """Get the updated campaign context with all results"""
        # Generate final report
        performance_report = self.telemetry_analyzer.generate_performance_report()

        # Update context with comprehensive results
        self.ctx.metadata.update(
            {
                "phase3_metrics_history": [
                    m.__dict__ for m in self.telemetry_analyzer.metrics_history
                ],
                "phase3_state_analyses": [
                    {
                        "timestamp": a.timestamp.isoformat(),
                        "stage": a.current_stage.value,
                        "confidence": a.confidence,
                        "bottlenecks": a.bottlenecks,
                        "needs_adjustment": a.needs_adjustment,
                        "suggested_actions": a.suggested_actions,
                        "error": a.error,
                    }
                    for a in self.state_analyses
                ],
                "phase3_needs_adjustment": self.needs_adjustment,
                "phase3_performance_report": performance_report,
                "phase3_anomalies": self.performance_anomalies,
                "phase3_duration_hours": self.duration_hours,
                "phase3_architecture": "concurrent_tasks_with_health_monitoring",
                "phase3_shadow_processes": self.shadow_manager.get_status(),
                "phase3_task_status": dict(self.task_status),
                "phase3_completed": datetime.now(timezone.utc).isoformat(),
            }
        )

        # Update final metrics
        if self.telemetry_analyzer.metrics_history:
            latest_metrics = self.telemetry_analyzer.metrics_history[-1]
            self.ctx.final_metrics.update(
                {
                    "coverage": getattr(latest_metrics, "coverage_hits", 0) / 1000.0,
                    "unique_crashes": getattr(latest_metrics, "crashes_found", 0),
                    "total_executions": getattr(latest_metrics, "total_executions", 0),
                }
            )

        logger.info(
            f"Phase 3 completed. Collected {len(self.telemetry_analyzer.metrics_history)} "
            f"metric windows. State analyses: {len(self.state_analyses)}. "
            f"Needs adjustment: {self.needs_adjustment}"
        )

        return self.ctx

    async def _telemetry_collection_task(self) -> None:
        """Continuously collect telemetry data without blocking."""
        logger.info("Telemetry collection task started")
        last_heartbeat = datetime.now(timezone.utc)
        last_log_time = datetime.now(timezone.utc)
        last_brief_update = datetime.now(timezone.utc)  # For frequent brief updates
        entries_processed = 0
        total_entries = 0

        while (
            not self.stop_event.is_set() and datetime.now(timezone.utc) < self.end_time
        ):
            try:
                # Update heartbeat
                current_time = datetime.now(timezone.utc)
                if (current_time - last_heartbeat).total_seconds() > 30:
                    self.task_status["telemetry_collection"][
                        "last_heartbeat"
                    ] = current_time
                    last_heartbeat = current_time

                # Read telemetry entries in batches
                entries = await self.telemetry_reader.read_batch(
                    max_entries=TELEMETRY_BATCH_SIZE
                )

                if entries:
                    await self._process_telemetry_entries(entries)
                    entries_processed = len(entries)
                    total_entries += entries_processed

                    # Brief real-time update every 10 seconds
                    if (current_time - last_brief_update).total_seconds() >= 10:
                        # Get quick performance snapshot
                        for (
                            instance_id,
                            aggregator,
                        ) in self.telemetry_analyzer.aggregators.items():
                            metrics = aggregator.get_current_metrics()
                            logger.info(
                                f"🔄 Real-time [{instance_id}]: "
                                f"Speed: {metrics.execution_rate:.1f} exec/s | "
                                f"Coverage: {metrics.coverage_hits:,} edges ({metrics.coverage_percentage:.1f}%) | "
                                f"Crashes: {metrics.crashes_found} | "
                                f"Corpus: {metrics.corpus_size}"
                            )
                        last_brief_update = current_time

                    # Log detailed progress every minute
                    if (current_time - last_log_time).total_seconds() >= 60:
                        logger.info(
                            f"Telemetry collection progress: "
                            f"{total_entries} total entries processed, "
                            f"{entries_processed} in last batch, "
                            f"{len(self.telemetry_analyzer.aggregators)} active instances"
                        )

                        # Log real-time performance metrics for each instance
                        logger.info("=== Real-time Performance Metrics ===")
                        for (
                            instance_id,
                            aggregator,
                        ) in self.telemetry_analyzer.aggregators.items():
                            metrics = aggregator.get_current_metrics()

                            # Calculate additional rates
                            window_duration = (
                                current_time - aggregator.current_window_start
                            ).total_seconds()
                            if window_duration > 0:
                                recent_exec_rate = (
                                    aggregator.executions / window_duration
                                )
                                recent_coverage_rate = (
                                    len(aggregator.coverage_hits) / window_duration
                                    if window_duration > 0
                                    else 0
                                )
                            else:
                                recent_exec_rate = 0
                                recent_coverage_rate = 0

                            logger.info(
                                f"📊 Instance [{instance_id}] Performance:\n"
                                f"  ⚡ Execution: {metrics.total_executions:,} total | "
                                f"{recent_exec_rate:.1f} exec/s\n"
                                f"  📈 Coverage: {metrics.coverage_hits:,} edges | "
                                f"{metrics.coverage_percentage:.2f}% | "
                                f"{recent_coverage_rate:.1f} edges/s\n"
                                f"  💥 Crashes: {metrics.crashes_found} found | "
                                f"{metrics.crash_rate:.2f} per 1M execs\n"
                                f"  📦 Corpus: {metrics.corpus_size} inputs | "
                                f"{metrics.corpus_growth_rate:.2f} inputs/min\n"
                                f"  🎯 Efficiency: {metrics.efficiency_score:.3f} | "
                                f"Path discovery: {metrics.path_discovery_rate:.2f} paths/min"
                            )
                        logger.info("=" * 40)

                        last_log_time = current_time
                else:
                    # Log if no entries for extended period
                    if (
                        current_time - last_log_time
                    ).total_seconds() >= 300:  # 5 minutes
                        logger.warning(
                            "No telemetry entries received in last 5 minutes"
                        )
                        last_log_time = current_time

                # Small sleep to prevent CPU spinning
                await asyncio.sleep(TELEMETRY_COLLECTION_INTERVAL)

            except Exception as e:
                logger.error(f"Error in telemetry collection: {e}")
                raise TelemetryError(f"Telemetry collection failed: {e}") from e

        logger.info(
            f"Telemetry collection task completed. Total entries processed: {total_entries}"
        )

    async def _process_telemetry_entries(self, entries: list[dict[str, Any]]) -> None:
        """Process telemetry entries into aggregators

        Args:
            entries: List of telemetry entries to process
        """
        # Track entry types for logging
        entry_types_count: dict[str, int] = {}

        async with self.data_lock:
            for entry in entries:
                instance_id = entry.get("instance_id", "default")
                entry_type = entry.get("type", "unknown")

                # Count entry types
                entry_types_count[entry_type] = entry_types_count.get(entry_type, 0) + 1

                # Create aggregator if needed
                if instance_id not in self.telemetry_analyzer.aggregators:
                    self.telemetry_analyzer.aggregators[instance_id] = (
                        TelemetryAggregator()
                    )

                # Process entry
                self.telemetry_analyzer.aggregators[
                    instance_id
                ].process_telemetry_entry(entry)

                # Log specific entry details for important types
                data_type = entry.get("data_type")
                if data_type == "COVERAGE_HIT" or data_type == 1:
                    if "coverage_hit" in entry:
                        edge_id = entry["coverage_hit"].get("edge_id", 0)
                        is_new = entry["coverage_hit"].get("is_new", False)
                        if is_new:
                            logger.debug(f"🆕 New coverage: edge {edge_id}")
                elif data_type == "CRASH_FOUND" or data_type == 2:
                    if "crash_found" in entry:
                        crash_type = entry["crash_found"].get("crash_type", "unknown")
                        signal = entry["crash_found"].get("signal", 0)
                        logger.info(f"🎯 Crash found: {crash_type} (signal {signal})")
                elif data_type == "CORPUS_GROW" or data_type == 5:
                    if "corpus_grow" in entry:
                        new_inputs = entry["corpus_grow"].get("new_inputs", 0)
                        total_size = entry["corpus_grow"].get("total_size", 0)
                        logger.info(
                            f"📈 Corpus growth: +{new_inputs} inputs (total: {total_size})"
                        )
                # Legacy format support
                elif entry_type == "coverage_hit":
                    edge_id = entry.get("edge_id", 0)
                    logger.debug(f"Coverage hit: edge {edge_id}")
                elif entry_type == "crash_found":
                    crash_type = entry.get("crash_type", "unknown")
                    signal = entry.get("signal", 0)
                    logger.info(f"🎯 Crash found: {crash_type} (signal {signal})")
                elif entry_type == "corpus_grow":
                    new_inputs = entry.get("new_inputs", 0)
                    total_size = entry.get("total_size", 0)
                    logger.info(
                        f"📈 Corpus growth: +{new_inputs} inputs (total: {total_size})"
                    )

        # Log entry type summary if we processed entries
        if entry_types_count:
            types_summary = ", ".join(f"{k}:{v}" for k, v in entry_types_count.items())
            logger.debug(f"Processed entry types: {types_summary}")

    async def _llm_analysis_task(self) -> None:
        """Perform periodic LLM analysis every 10 minutes as per workflow.md."""
        logger.info("LLM analysis task started")

        analysis_interval = timedelta(minutes=LLM_ANALYSIS_INTERVAL_MINUTES)
        last_analysis = datetime.now(timezone.utc)
        last_heartbeat = datetime.now(timezone.utc)
        analysis_count = 0
        successful_analyses = 0

        # Wait initial period to collect data
        logger.info("LLM analysis: waiting 30 seconds for initial data collection")
        await asyncio.sleep(30)  # 30 seconds initial wait

        while (
            not self.stop_event.is_set() and datetime.now(timezone.utc) < self.end_time
        ):
            try:
                current_time = datetime.now(timezone.utc)

                # Update heartbeat
                if (current_time - last_heartbeat).total_seconds() > 30:
                    self.task_status["llm_analysis"]["last_heartbeat"] = current_time
                    last_heartbeat = current_time

                # Check if 10 minutes have passed
                time_since_last = (current_time - last_analysis).total_seconds()
                if current_time - last_analysis >= analysis_interval:
                    analysis_count += 1
                    logger.info(
                        f"Performing scheduled LLM state analysis #{analysis_count} "
                        f"(10-minute interval, {time_since_last / 60:.1f} minutes since last)"
                    )

                    # Perform LLM analysis
                    try:
                        await self._perform_llm_analysis()
                        successful_analyses += 1
                        logger.info(
                            f"LLM analysis #{analysis_count} completed successfully "
                            f"({successful_analyses}/{analysis_count} success rate)"
                        )
                    except Exception as analysis_error:
                        logger.error(
                            f"LLM analysis #{analysis_count} failed: {analysis_error}"
                        )

                    last_analysis = current_time

                # Check every 30 seconds for timing
                await asyncio.sleep(30)

            except Exception as e:
                logger.error(f"Error in LLM analysis task: {e}")
                await asyncio.sleep(60)  # Back off on error

        logger.info(
            f"LLM analysis task completed. "
            f"Total analyses: {analysis_count}, "
            f"Successful: {successful_analyses}"
        )

    async def _perform_llm_analysis(self) -> None:
        """Perform LLM analysis on current metrics"""
        # Collect current metrics
        metrics_list = await self._collect_current_metrics()

        # Analyze each metric set
        for metrics in metrics_list:
            logger.info(f"Sending metrics to LLM for instance: {metrics.instance_id}")

            try:
                analysis = await _analyze_state_with_metrics(
                    self.llm_client,
                    metrics,
                    self.ctx,
                )

                await self._handle_analysis_result(analysis, metrics)

            except Exception as e:
                logger.error(f"Error in LLM analysis: {e}")

    async def _collect_current_metrics(self) -> list[PerformanceMetrics]:
        """Collect current metrics from all aggregators

        Returns:
            List of performance metrics for each instance
        """
        metrics_list = []

        async with self.data_lock:
            for instance_id, aggregator in self.telemetry_analyzer.aggregators.items():
                metrics = aggregator.get_current_metrics()
                metrics.instance_id = instance_id
                metrics_list.append(metrics)

        return metrics_list

    async def _handle_analysis_result(
        self, analysis: StateAnalysis, metrics: PerformanceMetrics
    ) -> None:
        """Handle the result of LLM analysis

        Args:
            analysis: The state analysis result
            metrics: The metrics that were analyzed
        """
        async with self.data_lock:
            self.state_analyses.append(analysis)

            if analysis.needs_adjustment:
                logger.warning(
                    f"LLM detected need for adjustment: {analysis.bottlenecks}"
                )
                self.needs_adjustment = True

                # Trigger shadow process creation if needed
                if analysis.suggested_actions:
                    await self._create_shadow_from_analysis(analysis, metrics)

    async def _create_shadow_from_analysis(
        self,
        analysis: StateAnalysis,
        metrics: PerformanceMetrics,
    ) -> None:
        """Create a shadow process based on LLM analysis

        Args:
            analysis: The state analysis with suggestions
            metrics: Current performance metrics
        """
        logger.info("Creating shadow process with new strategy")

        try:
            new_strategy = await _generate_optimized_strategy(
                self.llm_client,
                self.ctx,
                analysis,
                metrics,
            )

            if new_strategy:
                await self.shadow_manager.request_shadow(
                    reason=f"LLM adjustment: {', '.join(analysis.bottlenecks[:2])}",
                    new_strategy_code=new_strategy.get("custom_code", ""),
                    confidence=analysis.confidence,
                )
            else:
                logger.warning("Failed to generate optimized strategy")

        except Exception as e:
            logger.error(f"Failed to create shadow process: {e}")

    async def _anomaly_detection_task(self) -> None:
        """Perform real-time anomaly detection with immediate response."""
        logger.info("Anomaly detection task started")

        window_interval = timedelta(seconds=ANOMALY_CHECK_INTERVAL_SECONDS)
        last_window_rotation = datetime.now(timezone.utc)
        last_heartbeat = datetime.now(timezone.utc)
        anomaly_check_count = 0
        total_anomalies_detected = 0

        while (
            not self.stop_event.is_set() and datetime.now(timezone.utc) < self.end_time
        ):
            try:
                current_time = datetime.now(timezone.utc)

                # Update heartbeat
                if (current_time - last_heartbeat).total_seconds() > 30:
                    self.task_status["anomaly_detection"][
                        "last_heartbeat"
                    ] = current_time
                    last_heartbeat = current_time

                # Rotate windows and check for anomalies
                if current_time - last_window_rotation >= window_interval:
                    anomaly_check_count += 1
                    logger.debug(f"Performing anomaly check #{anomaly_check_count}")

                    anomalies_found = await self._check_and_handle_anomalies()
                    total_anomalies_detected += anomalies_found

                    if anomalies_found > 0:
                        logger.info(
                            f"Anomaly detection: found {anomalies_found} anomalies "
                            f"(total: {total_anomalies_detected})"
                        )
                    elif (
                        anomaly_check_count % 10 == 0
                    ):  # Log every 10 checks (~5 minutes)
                        logger.info(
                            f"Anomaly detection: {anomaly_check_count} checks completed, "
                            f"{total_anomalies_detected} total anomalies detected"
                        )

                    last_window_rotation = current_time

                # Check every 10 seconds for responsiveness
                await asyncio.sleep(10)

            except Exception as e:
                logger.error(f"Error in anomaly detection task: {e}")
                await asyncio.sleep(30)  # Back off on error

        logger.info(
            f"Anomaly detection task completed. "
            f"Total checks: {anomaly_check_count}, "
            f"Total anomalies: {total_anomalies_detected}"
        )

    async def _check_and_handle_anomalies(self) -> int:
        """Check for anomalies and handle critical ones

        Returns:
            int: Number of anomalies found
        """
        anomalies_found = 0

        async with self.data_lock:
            for instance_id, aggregator in self.telemetry_analyzer.aggregators.items():
                # Get current metrics
                metrics = aggregator.get_current_metrics()
                metrics.instance_id = instance_id

                # Detect anomalies
                anomalies = self.telemetry_analyzer.detect_anomalies(metrics)

                if anomalies:
                    logger.warning(f"Anomalies detected for {instance_id}: {anomalies}")
                    self.performance_anomalies.extend(anomalies)
                    anomalies_found += len(anomalies)

                    # Handle critical anomalies
                    await self._handle_critical_anomalies(anomalies, metrics)

                # Rotate window and save metrics
                rotated_metrics = aggregator.rotate_window()
                rotated_metrics.instance_id = instance_id
                self.telemetry_analyzer.metrics_history.append(rotated_metrics)

        return anomalies_found

    async def _handle_critical_anomalies(
        self,
        anomalies: list[str],
        metrics: PerformanceMetrics,
    ) -> None:
        """Handle critical anomalies with immediate response

        Args:
            anomalies: List of detected anomalies
            metrics: Current performance metrics
        """
        # Check for critical anomalies
        critical_anomalies = [
            a for a in anomalies if "dropped significantly" in a or "stagnation" in a
        ]

        if not critical_anomalies:
            return

        logger.info("Critical anomaly - triggering immediate LLM analysis")

        try:
            analysis = await _analyze_state_with_metrics(
                self.llm_client,
                metrics,
                self.ctx,
                anomalies=critical_anomalies,
            )

            await self._handle_analysis_result(analysis, metrics)

        except Exception as e:
            logger.error(f"Error in anomaly LLM analysis: {e}")


async def _start_champion_fuzzer_with_retry(
    runtime_client: RuntimeClient,
    strategy: dict[str, Any],
    target_path: str,
    max_retries: int = 3,
) -> str | None:
    """Start champion fuzzer with retry logic and enhanced monitoring

    Args:
        runtime_client: Runtime client for fuzzer control
        strategy: Fuzzing strategy
        target_path: Target program path
        max_retries: Maximum number of retry attempts

    Returns:
        Fuzzer ID if successful, None otherwise
    """
    last_error = None

    for attempt in range(max_retries):
        try:
            logger.info(
                f"Starting champion fuzzer (attempt {attempt + 1}/{max_retries})"
            )

            # Step 1: Compile target program
            logger.info(f"Compiling target program: {target_path}")
            compile_result = await runtime_client.compile_target(
                source_path=target_path,
                enable_asan=False,  # 🔧 LibAFL标准模式：禁用ASAN避免符号冲突
                enable_libafl_coverage=True,
                mode="AUTO_MODE",
            )

            if not compile_result.get("success", False):
                error_msg = f"Failed to compile target: {compile_result.get('error', 'unknown error')}"
                logger.error(error_msg)
                last_error = Exception(error_msg)

                # For compilation failures, retry immediately without backoff
                if attempt < max_retries - 1:
                    logger.info("Retrying compilation")
                    continue
                else:
                    break

            # Step 2: Get compiled binary path
            target_library_path = compile_result.get("output_path", "")
            if not target_library_path:
                error_msg = "Compilation succeeded but no output path returned"
                logger.error(error_msg)
                last_error = Exception(error_msg)
                continue

            logger.info(f"Target compiled successfully: {target_library_path}")

            # Step 3: Start fuzzer with compiled binary
            fuzzer_id = await runtime_client.start_fuzzer(
                target_path=target_path,
                strategy=strategy,
                target_library_path=target_library_path,
                fuzzer_type="champion",
            )

            if fuzzer_id:
                logger.info(f"Fuzzer started successfully with ID: {fuzzer_id}")
                logger.info(f"Using compiled target: {target_library_path}")

                # 等待一小段时间让Rust端初始化共享内存
                import asyncio

                await asyncio.sleep(3.0)

                return fuzzer_id
            else:
                logger.warning(f"No fuzzer ID returned on attempt {attempt + 1}")

        except Exception as e:
            last_error = e
            logger.error(f"Failed to start fuzzer on attempt {attempt + 1}: {e}")

            # Exponential backoff
            if attempt < max_retries - 1:
                wait_time = 2**attempt
                logger.info(f"Waiting {wait_time}s before retry")
                await asyncio.sleep(wait_time)

    logger.error(
        f"Failed to start champion fuzzer after {max_retries} attempts. Last error: {last_error}"
    )
    return None


async def _analyze_state_with_metrics(
    llm_client: LiteLLMClient,
    metrics: PerformanceMetrics,
    ctx: CampaignContext,
    anomalies: list[str] | None = None,
) -> StateAnalysis:
    """Analyze fuzzing state using metrics with LLM.

    Args:
        llm_client: LLM client for analysis
        metrics: Current performance metrics
        ctx: Campaign context
        anomalies: Optional list of detected anomalies

    Returns:
        State analysis from LLM
    """
    try:
        # Prepare metrics summary
        metrics_summary = f"""Performance Metrics Analysis:
- Instance: {metrics.instance_id}
- Total Executions: {metrics.total_executions:,}
- Execution Rate: {metrics.execution_rate:.1f} exec/s
- Coverage Hits: {metrics.coverage_hits:,}
- Unique Edges: {metrics.unique_edges:,}
- Path Discovery Rate: {metrics.path_discovery_rate:.2f} paths/min
- Crashes Found: {metrics.crashes_found}
- Crash Rate: {metrics.crash_rate:.2f} crashes/1M execs
- Corpus Size: {metrics.corpus_size}
- Corpus Growth Rate: {metrics.corpus_growth_rate:.2f} inputs/min
- Efficiency Score: {metrics.efficiency_score:.3f}"""

        if anomalies:
            metrics_summary += "\n\nDetected Anomalies:\n" + "\n".join(
                f"- {a}" for a in anomalies
            )

        # Generate prompt
        try:
            prompt = _prompt_manager.get_prompt(
                "analysis.state_analysis",
                target_path=ctx.target_path,
                strategy_name=(
                    ctx.strategy.get("name", "unknown") if ctx.strategy else "unknown"
                ),
                elapsed_hours=getattr(ctx, "elapsed_hours", 0.0),
                telemetry_summary=metrics_summary,
            )
        except Exception as e:
            logger.error(f"Failed to generate prompt: {e}")
            # Use fallback prompt
            prompt = _create_fallback_analysis_prompt(ctx, metrics_summary)

        # Get LLM analysis with retry
        response_text = await _call_llm_with_retry(
            llm_client,
            prompt,
            max_tokens=500,
            temperature=0.3,
            max_retries=2,
        )

        # Parse response
        analysis_data = json_repair.loads(response_text)

        # Ensure valid response
        if not isinstance(analysis_data, dict):
            analysis_data = {}

        # Map stage string to enum
        stage_str = str(analysis_data.get("current_stage", "unknown")).lower()
        stage_map = {
            "exploration": AnalysisStage.EXPLORATION,
            "exploitation": AnalysisStage.EXPLOITATION,
            "stagnation": AnalysisStage.STAGNATION,
            "unknown": AnalysisStage.UNKNOWN,
        }
        current_stage = stage_map.get(stage_str, AnalysisStage.UNKNOWN)

        return StateAnalysis(
            timestamp=datetime.now(timezone.utc),
            current_stage=current_stage,
            confidence=float(analysis_data.get("confidence", 0.5)),
            bottlenecks=list(analysis_data.get("bottlenecks", [])),
            needs_adjustment=bool(analysis_data.get("needs_adjustment", False)),
            suggested_actions=list(analysis_data.get("suggested_actions", [])),
            raw_response=response_text,
        )

    except Exception as e:
        logger.exception("Failed to analyze state with LLM: %s", e)
        # Return minimal analysis on failure
        return StateAnalysis(
            timestamp=datetime.now(timezone.utc),
            current_stage=AnalysisStage.UNKNOWN,
            confidence=0.0,
            bottlenecks=[f"Analysis failed: {e!s}"],
            needs_adjustment=False,
            error=str(e),
        )


def _create_fallback_analysis_prompt(
    ctx: CampaignContext,
    metrics_summary: str,
) -> str:
    """Create a fallback analysis prompt when prompt manager fails

    Args:
        ctx: Campaign context
        metrics_summary: Formatted metrics summary

    Returns:
        Fallback prompt string
    """
    return f"""Analyze the current fuzzing state for {ctx.target_path}.

{metrics_summary}

Respond with a JSON object containing:
- current_stage: One of "exploration", "exploitation", "stagnation", or "unknown"
- confidence: Float between 0 and 1
- bottlenecks: List of identified bottlenecks
- needs_adjustment: Boolean indicating if strategy adjustment is needed
- suggested_actions: List of suggested actions to improve performance

Focus on identifying performance issues and actionable improvements."""


async def _call_llm_with_retry(
    llm_client: LiteLLMClient,
    prompt: str,
    max_tokens: int = 500,
    temperature: float = 0.3,
    max_retries: int = 3,
) -> str:
    """Call LLM with retry logic and exponential backoff

    Args:
        llm_client: LLM client
        prompt: The prompt to send
        max_tokens: Maximum tokens for response
        temperature: LLM temperature
        max_retries: Maximum retry attempts

    Returns:
        LLM response text

    Raises:
        Exception: After all retries are exhausted
    """
    last_error = None

    for attempt in range(max_retries):
        try:
            response = llm_client.generate(
                prompt=prompt,
                max_tokens=max_tokens,
                temperature=temperature,
            )

            if response and response.strip():
                return response
            else:
                logger.warning(f"Empty LLM response on attempt {attempt + 1}")

        except Exception as e:
            last_error = e
            logger.warning(
                f"LLM call failed on attempt {attempt + 1}/{max_retries}: {e}"
            )

            # Exponential backoff
            if attempt < max_retries - 1:
                wait_time = 2**attempt
                await asyncio.sleep(wait_time)

    raise Exception(f"All LLM retries failed. Last error: {last_error}")


async def _generate_optimized_strategy(
    llm_client: LiteLLMClient,
    ctx: CampaignContext,
    analysis: StateAnalysis,
    metrics: PerformanceMetrics,
) -> dict[str, Any] | None:
    """Generate an optimized strategy based on analysis and current metrics.

    Args:
        llm_client: LLM client for strategy generation
        ctx: Campaign context with current strategy
        analysis: State analysis with bottlenecks and suggestions
        metrics: Current performance metrics

    Returns:
        New strategy dictionary or None if generation fails
    """
    try:
        # Prepare context for strategy optimization
        optimization_context = f"""Current Strategy Analysis:
- Stage: {analysis.current_stage.value}
- Bottlenecks: {", ".join(analysis.bottlenecks) if analysis.bottlenecks else "None"}
- Suggested Actions: {", ".join(analysis.suggested_actions) if analysis.suggested_actions else "None"}

Performance Metrics:
- Coverage: {metrics.coverage_hits:,} hits
- Execution Rate: {metrics.execution_rate:.1f} exec/s
- Path Discovery: {metrics.path_discovery_rate:.2f} paths/min
- Crashes: {metrics.crashes_found}
- Efficiency Score: {metrics.efficiency_score:.3f}

Target: {ctx.target_path}
Current Strategy: {ctx.strategy.get("name", "unknown") if ctx.strategy else "unknown"}"""

        # Generate optimization prompt
        try:
            prompt = _prompt_manager.get_prompt(
                "strategy_optimization",
                context=optimization_context,
                current_strategy=ctx.strategy,
            )
        except Exception as e:
            logger.error(f"Failed to generate optimization prompt: {e}")
            # Use fallback prompt
            prompt = _create_fallback_optimization_prompt(
                optimization_context,
                ctx.strategy,
            )

        # Get LLM to generate optimized strategy with retry
        response = await _call_llm_with_retry(
            llm_client,
            prompt,
            max_tokens=1000,
            temperature=0.7,
            max_retries=2,
        )

        # Parse response
        strategy_data = json_repair.loads(response)

        if not isinstance(strategy_data, dict):
            logger.error("Invalid strategy response from LLM")
            return None

        # Validate required fields
        required_fields = ["mutators", "scheduler", "feedback"]
        missing_fields = [
            field for field in required_fields if field not in strategy_data
        ]

        if missing_fields:
            logger.error(f"Generated strategy missing fields: {missing_fields}")
            return None

        # Add metadata
        strategy_data["name"] = (
            f"shadow_strategy_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
        )
        strategy_data["parent"] = (
            ctx.strategy.get("name", "unknown") if ctx.strategy else "unknown"
        )
        strategy_data["optimization_reason"] = (
            analysis.bottlenecks[0] if analysis.bottlenecks else "performance"
        )
        strategy_data["metadata"] = {
            "generation_method": "phase3_optimization",
            "analysis_stage": analysis.current_stage.value,
            "performance_metrics": {
                "coverage": metrics.coverage_hits,
                "exec_rate": metrics.execution_rate,
                "crashes": metrics.crashes_found,
            },
        }

        logger.info(f"Generated optimized strategy: {strategy_data['name']}")
        return strategy_data

    except Exception as e:
        logger.error(f"Failed to generate optimized strategy: {e}")
        return None


def _create_fallback_optimization_prompt(
    context: str,
    current_strategy: dict[str, Any] | None,
) -> str:
    """Create a fallback optimization prompt

    Args:
        context: Optimization context with metrics and analysis
        current_strategy: Current strategy being used

    Returns:
        Fallback prompt string
    """
    strategy_info = ""
    if current_strategy:
        mutators = current_strategy.get("mutators", [])
        strategy_info = f"""
Current Strategy Configuration:
- Mutators: {len(mutators)} ({", ".join(m.get("type", "unknown") for m in mutators[:3])}...)
- Scheduler: {current_strategy.get("scheduler", {}).get("type", "unknown")}
- Feedback: {current_strategy.get("feedback", {}).get("type", "unknown")}"""

    return f"""Generate an optimized fuzzing strategy based on the current performance analysis.

{context}
{strategy_info}

Generate a JSON response with an improved strategy configuration that addresses the identified bottlenecks.
The response must include:
- mutators: Array of mutator configurations
- scheduler: Scheduler configuration
- feedback: Feedback configuration
- custom_code: Optional Rust code for custom mutators

Focus on improving the specific performance issues identified."""
