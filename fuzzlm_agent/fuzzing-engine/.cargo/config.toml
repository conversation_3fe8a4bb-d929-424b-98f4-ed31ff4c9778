[build]
rustflags = ["-C", "link-args=-rdynamic"]

# Export coverage runtime symbols for dynamically loaded targets
[target.'cfg(not(windows))']
rustflags = [
    "-C", "link-args=-rdynamic",
    "-C", "link-args=-Wl,--export-dynamic"
]

# For Windows (if needed)
[target.'cfg(windows)']
rustflags = [
    "-C", "link-args=/EXPORT:__sanitizer_cov_trace_pc_guard",
    "-C", "link-args=/EXPORT:__sanitizer_cov_trace_pc_guard_init"
]