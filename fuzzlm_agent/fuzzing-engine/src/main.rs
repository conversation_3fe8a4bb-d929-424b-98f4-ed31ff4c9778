/*!
LibAFL Fuzzing Engine

工作流驱动的 LibAFL Fuzzing 引擎主程序，实现：
- gRPC 控制平面服务器
- 多实例管理和资源隔离
- 共享内存遥测数据生产
- Champion/Shadow 并行执行支持
*/

use mimalloc::MiMalloc;
#[global_allocator]
static GLOBAL: MiMalloc = MiMalloc;

// 确保链接LibAFL targets runtime以提供coverage符号
extern crate libafl_targets;

use anyhow::Result;
use clap::Parser;
use log::{error, info};
use std::net::SocketAddr;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::signal;

use fuzzing_engine::{start_grpc_server, InstanceManager, TelemetryProducer};

#[derive(Parser, Debug)]
#[command(name = "fuzzing-engine")]
#[command(about = "工作流驱动的 LibAFL Fuzzing 引擎")]
struct Args {
    /// gRPC 服务器地址
    #[arg(long, default_value = "127.0.0.1:50051")]
    grpc_address: String,

    /// 工作目录
    #[arg(long, default_value = "./runtime/workspace")]
    work_dir: PathBuf,

    /// 输出目录
    #[arg(long, default_value = "./runtime/output")]
    output_dir: PathBuf,

    /// 遥测数据流名称
    #[arg(long, default_value = "champion_main")]
    telemetry_stream: String,

    /// 共享内存缓冲区大小 (MB)
    #[arg(long, default_value = "64")]
    buffer_size_mb: usize,

    /// 日志级别
    #[arg(long, default_value = "info")]
    log_level: String,

    /// 启用调试模式
    #[arg(long)]
    debug: bool,
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();

    // 初始化日志
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or(&args.log_level))
        .init();

    info!("启动工作流驱动的 LibAFL Fuzzing Engine");
    info!("gRPC 地址: {}", args.grpc_address);
    info!("工作目录: {:?}", args.work_dir);
    info!("输出目录: {:?}", args.output_dir);
    info!("遥测流: {}", args.telemetry_stream);
    info!("缓冲区大小: {}MB", args.buffer_size_mb);

    // 创建工作目录
    std::fs::create_dir_all(&args.work_dir)?;
    std::fs::create_dir_all(&args.output_dir)?;

    // 初始化遥测数据生产者
    let buffer_size = args.buffer_size_mb * 1024 * 1024;
    let telemetry_producer =
        Arc::new(TelemetryProducer::new(&args.telemetry_stream, buffer_size).await?);
    info!("✓ 遥测数据生产者初始化完成");

    // 初始化实例管理器
    let instance_manager = Arc::new(InstanceManager::new(
        Arc::clone(&telemetry_producer),
        args.work_dir.clone(),
        args.output_dir.clone(),
    ));
    info!("✓ 实例管理器初始化完成");

    // 解析 gRPC 地址
    let grpc_addr: SocketAddr = args.grpc_address.parse()?;

    // 启动 gRPC 服务器并监听信号
    info!("🚀 启动 gRPC 服务器: {grpc_addr}");

    // 使用 select! 同时监听信号和 gRPC 服务器
    tokio::select! {
        // 监听 gRPC 服务器
        grpc_result = start_grpc_server(
            grpc_addr,
            Arc::clone(&instance_manager),
        ) => {
            match grpc_result {
                Ok(_) => {
                    info!("gRPC 服务器正常退出，正在关闭所有实例...");
                    if let Err(e) = instance_manager.shutdown().await {
                        error!("关闭实例失败: {e}");
                    }
                }
                Err(e) => {
                    error!("gRPC 服务器启动失败: {e}");
                    return Err(e);
                }
            }
        }

        // 监听 Ctrl+C 信号
        signal_result = signal::ctrl_c() => {
            match signal_result {
                Ok(_) => {
                    info!("收到停止信号，正在关闭所有实例...");
                    if let Err(e) = instance_manager.shutdown().await {
                        error!("关闭实例失败: {e}");
                    }
                    info!("实例关闭完成");
                }
                Err(e) => {
                    error!("信号处理错误: {e}");
                }
            }
        }
    }

    info!("Fuzzing Engine 正常退出");
    Ok(())
}
